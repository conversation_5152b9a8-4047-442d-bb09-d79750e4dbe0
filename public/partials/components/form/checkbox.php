<?php
function renderCheckbox(string $label, string $name = '', bool $isChecked = false): void
{
    $id = $name !== '' ? htmlspecialchars($name) : 'checkbox_' . uniqid();
    $labelId = $id . '_label';
    ?>
    <label for="<?= $id ?>" class="group relative inline-flex items-center cursor-pointer select-none p-2 -m-2">
        <!-- Native checkbox (visually hidden but still clickable) -->
        <input id="<?= $id ?>" name="<?= htmlspecialchars($name) ?>" type="checkbox"
            class="peer absolute inset-0 w-full h-full opacity-0 cursor-pointer" <?= $isChecked ? 'checked' : '' ?>>

        <!-- Custom box -->
        <span aria-hidden="true" class="flex-shrink-0 relative inline-flex items-center justify-center w-5 h-5 mr-2
                     before:content-[''] before:absolute before:inset-0 before:border before:rounded-md
                     before:border-gray-300
                     peer-checked:before:bg-red-500 peer-checked:before:border-red-500
                     rounded-md transition-colors">
            <!-- Check icon -->
            <svg class="absolute inset-0 w-4 h-3 m-auto stroke-white opacity-0
                        peer-checked:opacity-100 transition-opacity" viewBox="0 0 17 18" fill="none"
                stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <polyline points="1 9 7 14 15 4"></polyline>
            </svg>
        </span>

        <!-- Visible label -->
        <span id="<?= $labelId ?>" class="text-gray-900 select-none">
            <?= htmlspecialchars($label) ?>
        </span>
    </label>
    <?php
}
